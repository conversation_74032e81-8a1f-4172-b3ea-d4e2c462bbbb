import type { FetchOptions } from 'ofetch'
import type { ApiResponse, PaginatedResponse } from '~/types/api'

// HTTP 方法类型 - 移除这个类型定义，直接使用字符串字面量

// 创建API客户端
export const createApiClient = () => {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()

  return $fetch.create({
    baseURL: config.public.apiBase,
    
    // 请求拦截器
    onRequest({ options }) {
      // 添加认证头
      if (authStore.token) {
              options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${authStore.token}`
      } as any
      }

      // 添加Content-Type
      if (options.method && ['POST', 'PUT', 'PATCH'].includes(options.method.toUpperCase())) {
        options.headers = {
          ...options.headers,
          'Content-Type': 'application/json'
        } as any
      }
    },

    // 响应拦截器
    onResponse({ response }) {
      // 处理成功响应
      if (response.status >= 200 && response.status < 300) {
        return response._data
      }
    },

    // 错误处理
    onResponseError({ response }) {
      const status = response.status
      const data = response._data

      // 处理认证错误
      if (status === 401) {
        authStore.clearAuth()
        navigateTo('/login')
        throw createError({
          statusCode: 401,
          statusMessage: '认证失败，请重新登录'
        })
      }

      // 处理权限错误
      if (status === 403) {
        throw createError({
          statusCode: 403,
          statusMessage: data?.message || '权限不足'
        })
      }

      // 处理服务器错误
      if (status >= 500) {
        throw createError({
          statusCode: status,
          statusMessage: '服务器错误，请稍后重试'
        })
      }

      // 处理其他错误
      throw createError({
        statusCode: status,
        statusMessage: data?.message || '请求失败'
      })
    }
  })
}

// 全局API实例
export const api = createApiClient()

// API工具函数
export class ApiClient {
  private client: typeof api

  constructor() {
    this.client = createApiClient()
  }

  // GET请求
  async get<T>(url: string, options?: FetchOptions): Promise<ApiResponse<T>> {
    return this.client(url, { ...options, method: 'GET' })
  }

  // POST请求
  async post<T>(url: string, body?: Record<string, unknown>, options?: FetchOptions): Promise<ApiResponse<T>> {
    return this.client(url, { ...options, method: 'POST', body })
  }

  // PUT请求
  async put<T>(url: string, body?: Record<string, unknown>, options?: FetchOptions): Promise<ApiResponse<T>> {
    return this.client(url, { ...options, method: 'PUT', body })
  }

  // PATCH请求
  async patch<T>(url: string, body?: Record<string, unknown>, options?: FetchOptions): Promise<ApiResponse<T>> {
    return this.client(url, { ...options, method: 'PATCH', body })
  }

  // DELETE请求
  async delete<T>(url: string, options?: FetchOptions): Promise<ApiResponse<T>> {
    return this.client(url, { ...options, method: 'DELETE' })
  }

  // 分页查询
  async paginate<T = Record<string, unknown>>(
    url: string, 
    params?: {
      page?: number
      limit?: number
      search?: string
      sort?: string
      order?: 'asc' | 'desc'
      [key: string]: unknown
    }
  ): Promise<PaginatedResponse<T>> {
    const query = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          query.append(key, String(value))
        }
      })
    }

    const queryString = query.toString()
    const fullUrl = queryString ? `${url}?${queryString}` : url

    return this.client(fullUrl, { method: 'GET' })
  }

  // 文件上传
  async upload<T>(url: string, file: File, options?: FetchOptions): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    return this.client(url, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        // 不设置Content-Type，让浏览器自动设置
        ...options?.headers
      }
    })
  }

  // 批量上传
  async uploadMultiple<T>(url: string, files: File[], options?: FetchOptions): Promise<ApiResponse<T>> {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })

    return this.client(url, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        ...options?.headers
      }
    })
  }

  // 下载文件
  async download(url: string, filename?: string, options?: FetchOptions): Promise<void> {
    try {
      const response = await fetch(`${useRuntimeConfig().public.apiBase}${url}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${useAuthStore().token}`,
          ...options?.headers
        }
      })

      if (!response.ok) {
        throw new Error('下载失败')
      }

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Download error:', error)
      throw error
    }
  }
}

// 导出API客户端实例
export const apiClient = new ApiClient()

// 常用API端点
export const endpoints = {
  // 认证相关
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    profile: '/auth/profile',
    changePassword: '/auth/change-password',
    refresh: '/auth/refresh'
  },

  // 客户管理
  customers: {
    list: '/customers',
    create: '/customers',
    detail: (id: string) => `/customers/${id}`,
    update: (id: string) => `/customers/${id}`,
    delete: (id: string) => `/customers/${id}`,
    stats: '/customers/stats'
  },

  // 项目档案
  archives: {
    list: '/archives',
    create: '/archives',
    detail: (id: string) => `/archives/${id}`,
    update: (id: string) => `/archives/${id}`,
    delete: (id: string) => `/archives/${id}`,
    stats: '/archives/stats'
  },

  // 服务工单
  services: {
    list: '/services',
    create: '/services',
    detail: (id: string) => `/services/${id}`,
    update: (id: string) => `/services/${id}`,
    delete: (id: string) => `/services/${id}`,
    assign: (id: string) => `/services/${id}/assign`,
    stats: '/services/stats'
  },

  // 配置管理
  configurations: {
    list: '/configurations',
    create: '/configurations',
    detail: (id: string) => `/configurations/${id}`,
    update: (id: string) => `/configurations/${id}`,
    delete: (id: string) => `/configurations/${id}`,
    toggle: (id: string) => `/configurations/${id}/toggle`,
    stats: '/configurations/stats'
  },

  // SLA管理
  sla: {
    templates: '/sla/templates',
    createTemplate: '/sla/templates',
    template: (id: string) => `/sla/templates/${id}`,
    updateTemplate: (id: string) => `/sla/templates/${id}`,
    deleteTemplate: (id: string) => `/sla/templates/${id}`,
    stats: '/sla/stats',
    performance: (id: string) => `/sla/templates/${id}/performance`
  },

  // 通知服务
  notifications: {
    sendEmail: '/notifications/email',
    sendTemplateEmail: '/notifications/email/template',
    sendSms: '/notifications/sms',
    sendTemplateSms: '/notifications/sms/template',
    sendBulkSms: '/notifications/sms/bulk',
    testEmail: '/notifications/email/test',
    status: '/notifications/status'
  }
}
